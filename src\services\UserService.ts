import { gql } from 'graphql-request';
import Http from './http.class';

const httpInstance = new Http().instance;

export const LOGIN = gql`
    mutation Auth_login($email: String!, $password: String!, $role_id: Int!) {
        auth_login(body: { email: $email, password: $password, role_id: $role_id }) {
            id
            first_name
            last_name
            avatar
            role_id
            created_at
            token {
                access_token
                refresh_token
            }
        }
    }
`;

export const PROFILE = gql`
    query Auth_profile {
        auth_profile {
            id
            oms_user_id
            username
            email
            full_name
            is_active
            auth_group
            oms_emp_code
            oms_organization_id
            oms_organization_name
            oms_ad_account
            oms_position_name
            oms_company_code
            oms_company_name
            oms_div_name
            oms_dep_name
            userAreas {
                user_id
                area_id
                created_at
                area {
                    id
                    name
                    code
                    parentArea {
                        id
                        name
                        code
                        parentArea {
                            id
                            name
                            code
                        }
                    }
                }
            }
            userAreaRoles {
                id
                user_id
                area_id
                role_id
                area {
                    id
                    name
                    code
                    parentArea {
                        id
                        name
                        code
                        parentArea {
                            id
                            name
                            code
                        }
                    }
                }
            }
        }
    }
`;

export const LOGOUT = gql`
    query Auth_logout {
        auth_logout
    }
`;

export const REFRESH = gql`
    mutation Auth_refresh_token($refresh_token: String!) {
        auth_refresh_token(input: { refresh_token: $refresh_token }) {
            access_token
            refresh_token
            expires_in
            user {
                id
                oms_user_id
                username
                email
                full_name
                created_at
                deleted_at
            }
        }
    }
`;

export const USER_LIST_PAGINATE = gql`
    query Users_list_paginate($page: Int!, $limit: Int!, $search: String, $filters: [String!], $sort: String) {
        users_list_paginate(body: { page: $page, limit: $limit, search: $search, filters: $filters, sort: $sort }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
                created_at
                userAreas {
                    user_id
                    area_id
                    area {
                        id
                        type
                        name
                        code
                        parent_area_id
                    }
                }
                userAreaRoles {
                    user_id
                    area_id
                    role_id
                    role {
                        id
                        name
                    }
                }
            }
        }
    }
`;

export const USER_DELETE = gql`
    mutation Users_delete($id: Int!) {
        users_delete(id: $id)
    }
`;

export const USER_CREATE = gql`
    mutation Users_create($body: UserCreateInputDto!) {
        users_create(body: $body) {
            id
        }
    }
`;

export const USER_UPDATE = gql`
    mutation Users_update($id: Int!, $body: UserUpdateInputDto!) {
        users_update(id: $id, body: $body) {
            id
            first_name
            last_name
            phone_number
            email
            address
            avatar_id
            gender_id
            role_id
            status_id
            birthday
            avatar {
                id
                file_name
                file_url
            }
        }
    }
`;

export const USER_DETAIL = gql`
    query User_detail($id: String!) {
        user_detail(id: $id) {
            id
            oms_user_id
            username
            email
            full_name
            is_active
            oms_emp_code
            oms_organization_id
            oms_organization_name
            oms_ad_account
            oms_position_name
            oms_company_code
            oms_company_name
            oms_div_name
            oms_dep_name
            auth_group
            updated_at
            created_at
            userAreas {
                user_id
                area_id
                area {
                    id
                    type
                    name
                    code
                    parent_area_id
                    parentArea {
                        id
                        type
                        name
                        code
                        parent_area_id
                        parentArea {
                            id
                            type
                            name
                            code
                        }
                    }
                }
            }
            userAreaRoles {
                id
                user_id
                area_id
                role_id
                created_at
                role {
                    id
                    name
                }
            }
            creator {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
            updater {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
        }
    }
`;

export const USER_UPDATE_PROFILE = gql`
    mutation Auth_update($body: UserUpdateInputDto!) {
        auth_update(body: $body) {
            id
            first_name
            last_name
            phone_number
            email
            avatar_id
            role_id
            status_id
            gender_id
            birthday
            address
        }
    }
`;

export const USER_RESET_PASSWORD = gql`
    mutation Users_reset_pass($id: Int!, $password: String!, $confirm_password: String!) {
        users_reset_pass(id: $id, body: { password: $password, confirm_password: $confirm_password })
    }
`;

export const USERS_CHANGE_GROUPS = gql`
    mutation Users_change_groups($id: Int!, $group_ids: [Float!]!) {
        users_change_groups(id: $id, body: { group_ids: $group_ids })
    }
`;

export const AUTH_UPDATE = gql`
    mutation Auth_update($body: ProfileInputDto!) {
        auth_update(body: $body) {
            id
        }
    }
`;

export const AUTH_CHANGE_PASSWORD = gql`
    mutation Auth_change_password($old_password: String!, $password: String!, $confirm_password: String!) {
        auth_change_password(
            body: { old_password: $old_password, password: $password, confirm_password: $confirm_password }
        )
    }
`;

export const USER_FORGOT_PASSWORD = gql`
    mutation Auth_forgot_pass($body: ForgotPasswordDto!) {
        auth_forgot_pass(body: $body)
    }
`;

export const USER_CHANGE_PASSWORD = gql`
    mutation Auth_reset_pass($body: ResetPasswordDto!) {
        auth_reset_pass(body: $body)
    }
`;

export const MICROSOFT_LOGIN = gql`
    mutation Auth_microsoftLogin($token: String!, $role_id: Int!) {
        auth_microsoftLogin(body: { token: $token, role_id: $role_id }) {
            id
            first_name
            last_name
            avatar
            role_id
            created_at
            token {
                access_token
                refresh_token
            }
        }
    }
`;

export const AUTH_GET_MICROSOFT_LOGIN_URL = gql`
    query Auth_get_azure_login_url($email: String!) {
        auth_get_azure_login_url(email: $email) {
            url
        }
    }
`;

export const AUTH_LOGIN_WITH_AZURE_CODE = gql`
    mutation Auth_login_with_azure_code($code: String!) {
        auth_login_with_azure_code(code: $code) {
            access_token
            refresh_token
            expires_in
            user {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
        }
    }
`;

export const USER_LIST_ALL = gql`
    query Users_list_all($search: String, $filters: [String!], $sort: String) {
        users_list_all(body: { search: $search, filters: $filters, sort: $sort }) {
            id
            oms_user_id
            username
            email
            full_name
            is_active
            oms_emp_code
            oms_organization_id
            oms_organization_name
            oms_ad_account
            oms_position_name
            oms_company_code
            oms_company_name
            oms_div_name
            oms_dep_name
            auth_group
        }
    }
`;

export const CREATE_USER = gql`
    mutation Create_user($body: UserCreateInputDto!) {
        create_user(body: $body) {
            id
        }
    }
`;

export const UPDATE_USER = gql`
    mutation Update_user($id: String!, $auth_group: Int) {
        update_user(id: $id, body: { auth_group: $auth_group }) {
            id
        }
    }
`;

export const UPDATE_USER_AREAS = gql`
    mutation Update_user_areas($id: String!, $area_ids: [String!]!) {
        update_user_areas(id: $id, body: { area_ids: $area_ids })
    }
`;

export const OMS_GET_EMPLOYEE_INFO = gql`
    mutation Oms_get_employee_info($username: String!) {
        oms_get_employee_info(input: { username: $username }) {
            responseData {
                employeeId
                empCode
                organizationId
                organizationName
                adAcount
                t_FullName
                e_FullName
                nickName
                positionName
                businessUnit
                companyCode
                companyName
                div_Name
                dep_Name
                subDep_Name
                sec_Name
                email
                location
                employeeOrganizationRelationTypeId
                empOrgLevel
                cctR_Dept
                cctR_Over
                managerEmail
                tel
                mobileNo
            }
        }
    }
`;

export const exportUsers = (body: Readonly<{ search?: string; filters?: string[]; sort?: string }>) =>
    httpInstance.post('/users/export', body, {
        responseType: 'blob',
    });

export const ACTIVATE_USER = gql`
    mutation Activate_user($id: String!) {
        activate_user(id: $id) {
            id
        }
    }
`;

export const DEACTIVATE_USER = gql`
    mutation Deactivate_user($id: String!) {
        deactivate_user(id: $id) {
            id
        }
    }
`;
